/* models */
const Project = require('../models/project.model');
const uploadCertificateService = require('../services/upload-certificate.service');
const memberService = require('../services/member.service');

/* create project */
exports.createProject = async project => {
  return await Project.create(project);
};

/**
 * Update By Id
 *
 * @param {*} id
 * @param {*} project
 * @returns
 */
exports.updateProject = async (id, project) => {
  return Project.findByIdAndUpdate(id, { $set: project }, { new: true }).populate([
    {
      path: 'account',
      select: { name: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Delete By Id
 *
 * @param {*} id
 * @returns
 */
exports.deleteProject = async (id, deletedAt) => {
  return Project.findByIdAndUpdate(id, { $set: deletedAt });
};

/**
 * Get All Projects
 *
 * @param {*} page
 * @param {*} perPage
 * @param {*} account
 * @returns
 */
exports.getAllProjects = async (filterData, sort = -1) => {
  return Project.find(filterData)
    .populate([
      {
        path: 'account',
        select: { name: 1, _id: 1 },
        strictPopulate: false,
      },
    ])
    .sort({ createdAt: sort });
};

/**
 * Get All Projects with ProjectNumber - Title
 *
 * @param {*} filterData
 * @param {*} sort
 * @returns
 */
exports.getAllProject = async (filterData, sort = -1) => {
  return Project.aggregate([
    { $match: filterData },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        as: 'account',
      },
    },
    {
      $unwind: {
        path: '$account',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $addFields: {
        title: {
          $cond: {
            if: {
              $and: [
                { $ne: ['$projectNumber', ''] },
                { $ne: [{ $ifNull: ['$projectNumber', null] }, null] },
              ],
            },
            then: {
              $concat: [{ $ifNull: ['$projectNumber', ''] }, ' – ', { $ifNull: ['$title', ''] }],
            },
            else: '$title',
          },
        },
      },
    },
    {
      $project: {
        _id: 1,
        title: 1,
        projectNumber: 1,
        client: 1,
        defaultIdentifier: 1,
        isActive: 1,
        isDefault: 1,
        isDeletable: 1,
        status: 1,
        deletedBy: 1,
        deletedAt: 1,
        createdAt: 1,
        updatedAt: 1,
        __v: 1,
        account: {
          _id: '$account._id',
          name: '$account.name',
        },
      },
    },
    { $sort: { createdAt: sort } },
  ]);
};

/**
 * Get Project By Name
 *
 * @param {*} projectName
 * @returns
 */
exports.getProjectByName = async (projectName, account, isDefault) => {
  return Project.findOne({
    $and: [
      { account: account },
      { title: projectName },
      { isDefault: isDefault },
      { deletedAt: null },
    ],
  });
};

/**
 * Get Project By Id
 *
 * @param {*} id
 * @returns
 */
exports.getProjectById = async (id, account) => {
  return Project.findOne(
    {
      $and: [{ _id: id }, { account: account }, { deletedAt: null }],
    },
    { createdAt: 0, updatedAt: 0, __v: 0 }
  ).populate([
    {
      path: 'account',
      select: { name: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Get all default project
 *
 * @param {*} account
 * @returns
 */
exports.getDefaultProject = async account => {
  return Project.findOne(
    {
      $and: [
        { account: account },
        { isDefault: false },
        { defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER },
        { deletedAt: null },
      ],
    },
    { createdAt: 0, updatedAt: 0, __v: 0 }
  ).populate([
    {
      path: 'account',
      select: { name: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Get all default project
 *
 * @param {*} account
 * @returns
 */
exports.getDefaultProjects = async account => {
  return Project.find(
    {
      $and: [
        { account: account },
        { isDefault: true },
        { defaultIdentifier: global.constant.DEFAULT_DATA_IDENTIFIER },
        { deletedAt: null },
      ],
    },
    { createdAt: 0, updatedAt: 0, __v: 0 }
  ).populate([
    {
      path: 'account',
      select: { name: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Get All Projects details
 *
 * @param {*} page
 * @param {*} perPage
 * @param {*} projectId
 * @returns
 */
exports.getAllProjectsCertificates = async (year, month, page, perPage, filterData) => {
  const skipCount = page * perPage;
  return await Project.aggregate([
    {
      $match: filterData,
    },
    {
      $lookup: {
        from: 'users',
        localField: 'account',
        foreignField: 'account',
        pipeline: [
          {
            $project: {
              _id: 1,
              fullName: { $concat: ['$firstName', ' ', '$lastName'] },
              email: 1,
              profileImage: 1,
            },
          },
        ],
        as: 'user',
      },
    },
    {
      $unwind: '$user',
    },
    {
      $lookup: {
        from: 'functions',
        localField: '_id',
        foreignField: 'project',
        as: 'functionDetails',
      },
    },
    { $unwind: '$functionDetails' },
    {
      $addFields: {
        function: {
          functionId: '$functionDetails._id',
          functionName: '$functionDetails.functionName',
        },
      },
    },
    {
      $unset: 'functionDetails',
    },
    {
      $lookup: {
        from: 'upload-certificates',
        localField: 'user._id',
        foreignField: 'user',
        pipeline: [
          {
            $project: {
              _id: 1,
              files: {
                $map: {
                  input: {
                    $cond: [
                      { $and: [{ $ne: [year, null] }, { $ne: [month, null] }] },
                      {
                        $filter: {
                          input: '$files',
                          as: 'file',
                          cond: {
                            $and: [
                              { $eq: [{ $year: '$$file.toDate' }, year] },
                              { $eq: [{ $month: '$$file.toDate' }, month] },
                            ],
                          },
                        },
                      },
                      '$files',
                    ],
                  },
                  as: 'file',
                  in: {
                    name: '$$file.name',
                    validityDate: '$$file.validityDate',
                    fromDate: '$$file.fromDate',
                    toDate: '$$file.toDate',
                    fileName: '$$file.fileName',
                    link: '$$file.link',
                  },
                },
              },
            },
          },
        ],
        as: 'certificate.uploads',
      },
    },
    { $unwind: '$certificate' },
    { $unwind: '$certificate.uploads' },
    { $unwind: '$certificate.uploads.files' },
    {
      $addFields: {
        certificate: { $ifNull: ['$certificate', {}] },
      },
    },
    {
      $sort: {
        'certificate.validityDate': 1,
      },
    },
    {
      $group: {
        _id: {
          projectId: '$_id',
          userDetails: '$user',
        },
        title: { $first: '$title' },
        standByTypes: { $first: '$standByTypes' },
        defaultIdentifier: { $first: '$defaultIdentifier' },
        function: { $first: '$function' },
        certificate: { $addToSet: '$certificate.uploads.files' },
      },
    },
    {
      $project: {
        _id: '$_id.projectId',
        title: 1,
        standByTypes: 1,
        defaultIdentifier: 1,
        user: '$_id.userDetails',
        function: 1,
        certificate: 1,
      },
    },
    {
      $skip: skipCount,
    },
    {
      $limit: perPage,
    },
  ]);
};

/**
 * Get All Project Details
 *
 * @param {*} filter
 */
exports.getAllProjectsDetails = async (filter, functionsData, memberData) => {
  const userUploadedCertificate = await uploadCertificateService.getUserUploadCertificate(filter);

  const responseStructure = functionsData
    .map(func => {
      let usersWithCertificates = memberData
        .filter(member => member.function._id.toString() === func._id.toString())
        .filter(member => {
          if (filter.rotation && filter.rotation !== 'all') {
            return member.rotation === filter.rotation;
          }
          return true;
        })
        .map(member => {
          const userCertificates = userUploadedCertificate.filter(
            cert => cert.user.toString() === member.user?._id.toString()
          );

          return {
            _id: member.user?._id,
            name:
              (member.user?.callingName ? member.user.callingName : member.user?.firstName) +
              ' ' +
              member.user?.lastName,
            rotation: member.rotation,
            userCertificates,
          };
        });

      if (filter.rotation && filter.rotation !== 'all' && usersWithCertificates.length === 0) {
        return null;
      }

      return { [func.functionName]: { id: func._id, data: usersWithCertificates } };
    })
    .filter(Boolean);

  return responseStructure;
};

/**
 * Get Project Details - Optimized version
 *
 * @param {*} filter
 * @param {*} profileFunctions
 * @param {*} userProfileFunctionData
 * @returns
 */
exports.getProjectsDetailsOptimized = async (filter, profileFunctions, userProfileFunctionData) => {
  // Use optimized member service
  const members = await memberService.getAllMemberOptimized({
    account: filter.account,
    deletedAt: null,
  });

  const userUploadedCertificate = await uploadCertificateService.getUserUploadCertificate(filter);

  const responseStructure = profileFunctions
    .map(func => {
      let usersWithCertificates = userProfileFunctionData
        .filter(user => user.profileFunction && user.profileFunction.toString() === func._id.toString())
        .slice(0, 25) // Limit to 25 users as requested
        .map(user => {
          const userCertificates = userUploadedCertificate.filter(
            cert => cert.user.toString() === user._id.toString()
          );

          return {
            _id: user._id,
            name: (user.callingName ? user.callingName : user.firstName) + ' ' + user.lastName,
            rotation: null, // Will be set from member data if available
            userCertificates,
          };
        });

      // Add rotation information from members
      usersWithCertificates = usersWithCertificates.map(userWithCert => {
        const memberInfo = members.find(
          member => member.user && member.user._id.toString() === userWithCert._id.toString()
        );
        return {
          ...userWithCert,
          rotation: memberInfo ? memberInfo.rotation : null,
        };
      });

      if (filter.rotation && filter.rotation !== 'all') {
        usersWithCertificates = usersWithCertificates.filter(
          user => user.rotation === filter.rotation
        );
      }

      if (usersWithCertificates.length === 0) {
        return null;
      }

      return { [func.name]: { id: func._id, data: usersWithCertificates } };
    })
    .filter(Boolean);

  return responseStructure;
};

/**
 * Get Project Details
 *
 * @param {*} filter
 * @param {*} profileFunctions
 * @param {*} userProfileFunctionData
 * @returns
 */
exports.getProjectsDetails = async (filter, profileFunctions, userProfileFunctionData) => {
  const members = await memberService.getAllMember({
    account: filter.account,
    deletedAt: null,
  });

  const userUploadedCertificate = await uploadCertificateService.getUserUploadCertificate(filter);

  const responseStructure = profileFunctions.map(profileFunction => {
    const usersWithCertificates = userProfileFunctionData
      .filter(user => user.profileFunction.toString() === profileFunction._id.toString())
      .map(user => {
        const certificates = userUploadedCertificate.filter(
          cert => cert.user.toString() === user._id.toString()
        );

        let userMembers = members.filter(m => m.user._id.toString() === user._id.toString());

        if (filter.rotation && filter.rotation !== 'all') {
          userMembers = userMembers.filter(m => m.rotation === filter.rotation);
        }

        const latestMember = userMembers.sort(
          (memberOne, memberTwo) => new Date(memberTwo.updatedAt) - new Date(memberOne.updatedAt)
        )[0];

        if (filter.rotation && filter.rotation !== 'all' && !latestMember) return null;

        return {
          _id: user._id,
          name: (user.callingName ? user.callingName : user.firstName) + ' ' + user.lastName,
          userCertificates: certificates,
          rotation: latestMember ? latestMember.rotation : null,
        };
      })
      .filter(Boolean);

    return {
      [profileFunction.name]: {
        id: profileFunction._id,
        data: usersWithCertificates,
      },
    };
  });

  const filteredResponseStructure =
    filter.rotation && filter.rotation !== 'all'
      ? responseStructure.filter(entry => {
          const key = Object.keys(entry)[0];
          return entry[key].data.length > 0;
        })
      : responseStructure;

  return filteredResponseStructure;
};

/**
 * Get Project By Approver
 *
 * @param {*} filter
 * @returns
 */
exports.getProjectsByApprover = async filter => {
  return Project.find(filter);
};

/**
 * Report Calculation
 *
 * @param {*} filter
 * @returns
 */
exports.reportCalculation = async filter => {
  let agreegateFunction = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'locations',
        localField: '_id',
        foreignField: 'project',
        pipeline: [
          {
            $match: { deletedAt: null },
          },
          { $group: { _id: null, total: { $sum: 1 } } },
        ],
        as: 'locations',
      },
    },
    {
      $unwind: {
        path: '$locations',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'assets',
        localField: '_id',
        foreignField: 'project',
        pipeline: [
          {
            $match: { deletedAt: null },
          },
          { $group: { _id: null, total: { $sum: 1 } } },
        ],
        as: 'assets',
      },
    },
    {
      $unwind: {
        path: '$assets',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'reports',
        localField: '_id',
        foreignField: 'project',
        pipeline: [
          {
            $match: { deletedAt: null },
          },
          { $group: { _id: '$type', total: { $sum: 1 } } },
        ],
        as: 'reports',
      },
    },
    {
      $lookup: {
        from: 'reports',
        localField: '_id',
        foreignField: 'project',
        pipeline: [
          {
            $match: { deletedAt: null },
          },
          {
            $project: {
              _id: 1,
              reportDurations: 1,
            },
          },
          {
            $lookup: {
              from: 'report-questions',
              localField: '_id',
              foreignField: 'report',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                { $group: { _id: null, total: { $sum: '$duration' } } },
              ],
              as: 'reportDurations',
            },
          },
          {
            $unwind: '$reportDurations',
          },
        ],
        as: 'reportQuestionDurations',
      },
    },
    {
      $lookup: {
        from: 'user-reports',
        localField: '_id',
        foreignField: 'project',
        pipeline: [
          {
            $match: { status: 'closed', deletedAt: null },
          },
          {
            $group: {
              _id: {
                report: '$report',
                location: '$location',
                asset: { $ifNull: ['$asset', ''] },
              },
              id: { $push: '$_id' },
            },
          },
          {
            $project: { _id: 1, id: 1, reportDuration: 1 },
          },
          {
            $lookup: {
              from: 'report-questions',
              localField: '_id.report',
              foreignField: 'report',
              pipeline: [
                {
                  $match: { deletedAt: null },
                },
                { $group: { _id: null, total: { $sum: '$duration' } } },
              ],
              as: 'reportDuration',
            },
          },
          {
            $unwind: '$reportDuration',
          },
        ],
        as: 'userReportsDurations',
      },
    },
    {
      $addFields: {
        locationBaseReportCal: {
          $multiply: [
            {
              $sum: {
                $map: {
                  input: {
                    $filter: {
                      input: '$reports',
                      as: 'report',
                      cond: { $eq: ['$$report._id', 'location'] },
                    },
                  },
                  as: 'report',
                  in: '$$report.total',
                },
              },
            },
            '$locations.total',
          ],
        },
        assetPerLocationReportCal: {
          $multiply: [
            {
              $sum: {
                $map: {
                  input: {
                    $filter: {
                      input: '$reports',
                      as: 'report',
                      cond: { $eq: ['$$report._id', 'asset_per_location'] },
                    },
                  },
                  as: 'report',
                  in: '$$report.total',
                },
              },
            },
            { $multiply: ['$assets.total', 2] },
          ],
        },
        multipleAssetsReportCal: {
          $multiply: [
            {
              $sum: {
                $map: {
                  input: {
                    $filter: {
                      input: '$reports',
                      as: 'report',
                      cond: { $eq: ['$$report._id', 'multiple_assets'] },
                    },
                  },
                  as: 'report',
                  in: '$$report.total',
                },
              },
            },
            '$assets.total',
          ],
        },
      },
    },
    {
      $addFields: {
        totalReportCal: {
          $sum: [
            '$locationBaseReportCal',
            '$assetPerLocationReportCal',
            '$multipleAssetsReportCal',
          ],
        },
        reportDurations: { $sum: '$reportQuestionDurations.reportDurations.total' },
        userDurations: { $sum: '$userReportsDurations.reportDuration.total' },
      },
    },
    {
      $addFields: {
        totalReportDurations: { $multiply: ['$totalReportCal', '$reportDurations'] },
      },
    },
    {
      $addFields: {
        reportProgressInPercent: {
          $cond: {
            if: { $eq: ['$totalReportDurations', 0] },
            then: 0,
            else: {
              $round: [
                { $divide: [{ $multiply: ['$userDurations', 100] }, '$totalReportDurations'] },
                2,
              ],
            },
          },
        },
      },
    },
    {
      $project: {
        _id: 1,
        title: 1,
        locations: 1,
        assets: 1,
        reports: 1,
        locationBaseReportCal: 1,
        assetPerLocationReportCal: 1,
        multipleAssetsReportCal: 1,
        totalReportCal: 1,
        reportDurations: 1,
        totalReportDurations: 1,
        userDurations: 1,
        reportProgressInPercent: 1,
      },
    },
  ];
  return await Project.aggregate(agreegateFunction);
};
